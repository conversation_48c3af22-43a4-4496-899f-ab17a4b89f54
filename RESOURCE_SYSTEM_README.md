# Qt QRC 资源系统模拟实现

这是一个模拟Qt QRC（Qt Resource Collection）功能的C++实现，可以将文件嵌入到可执行文件中，并提供统一的资源访问接口。

## 功能特性

- ✅ 将任意文件嵌入到可执行文件中
- ✅ 提供类似Qt QResource的API接口
- ✅ 支持路径别名和目录结构
- ✅ 兼容Qt .qrc文件格式
- ✅ 支持JSON配置文件格式
- ✅ 自动资源注册机制
- ✅ 跨平台支持

## 文件结构

```
resource_system.h           # 资源系统头文件
resource_system.cpp         # 资源系统实现
resource_generator.py       # 资源生成工具（类似rcc）
example_resources.json      # JSON配置示例
example_qrc.qrc            # QRC配置示例
resource_example.cpp       # 使用示例
```

## 使用方法

### 1. 配置资源文件

#### 方式一：使用JSON配置（推荐）

创建 `resources.json`：
```json
{
    "resources": [
        {
            "file": "data/config.txt",
            "path": "/config/app.conf"
        },
        {
            "file": "images/icon.png",
            "path": "/images/icon.png"
        }
    ]
}
```

#### 方式二：使用Qt QRC格式

创建 `resources.qrc`：
```xml
<!DOCTYPE RCC>
<RCC version="1.0">
    <qresource prefix="/config">
        <file alias="app.conf">data/config.txt</file>
    </qresource>
    <qresource prefix="/images">
        <file>images/icon.png</file>
    </qresource>
</RCC>
```

### 2. 生成资源代码

使用资源生成器将配置转换为C++代码：

```bash
# 使用JSON配置
python3 resource_generator.py resources.json generated_resources.cpp

# 使用QRC配置
python3 resource_generator.py --qrc resources.qrc generated_resources.cpp
```

### 3. 集成到CMake项目

在 `CMakeLists.txt` 中添加：

```cmake
# 资源系统文件
set(RESOURCE_SYSTEM_SOURCES resource_system.cpp)
set(RESOURCE_SYSTEM_HEADERS resource_system.h)

# 生成资源文件
set(RESOURCE_CONFIG "${CMAKE_CURRENT_SOURCE_DIR}/resources.json")
set(GENERATED_RESOURCES "${CMAKE_CURRENT_BINARY_DIR}/generated_resources.cpp")

add_custom_command(
    OUTPUT ${GENERATED_RESOURCES}
    COMMAND python3 "${CMAKE_CURRENT_SOURCE_DIR}/resource_generator.py" 
            "${RESOURCE_CONFIG}" "${GENERATED_RESOURCES}"
    DEPENDS ${RESOURCE_CONFIG} resource_generator.py
    COMMENT "Generating embedded resources"
)

# 添加到目标
add_executable(MyApp 
    main.cpp
    ${RESOURCE_SYSTEM_SOURCES}
    ${GENERATED_RESOURCES}
)
```

### 4. 在代码中使用资源

```cpp
#include "resource_system.h"

int main() {
    // 方式一：使用ResourceManager
    auto& rm = ResourceSystem::ResourceManager::instance();
    
    // 检查资源是否存在
    if (rm.hasResource("/config/app.conf")) {
        // 获取资源内容
        std::string config = rm.getResourceAsString("/config/app.conf");
        std::cout << "Config: " << config << std::endl;
        
        // 保存资源到文件
        rm.saveResourceToFile("/config/app.conf", "extracted_config.txt");
    }
    
    // 列出所有资源
    auto resources = rm.listResources();
    for (const auto& path : resources) {
        std::cout << "Resource: " << path << std::endl;
    }
    
    // 方式二：使用QResource接口（兼容Qt）
    QResource resource("/images/icon.png");
    if (resource.isValid()) {
        const unsigned char* data = resource.data();
        size_t size = resource.size();
        // 处理二进制数据...
    }
    
    // 方式三：使用便利函数
    std::string content = ResourceSystem::loadResourceAsString("/config/app.conf");
    std::vector<unsigned char> bytes = ResourceSystem::loadResourceAsBytes("/images/icon.png");
    
    return 0;
}
```

## API 参考

### ResourceManager 类

```cpp
class ResourceManager {
public:
    static ResourceManager& instance();
    
    // 资源访问
    bool hasResource(const std::string& path) const;
    std::string getResourceAsString(const std::string& path) const;
    std::vector<unsigned char> getResourceAsBytes(const std::string& path) const;
    
    // 资源列举
    std::vector<std::string> listResources() const;
    std::vector<std::string> listResourcesInDirectory(const std::string& directory) const;
    
    // 资源导出
    bool saveResourceToFile(const std::string& resourcePath, const std::string& filePath) const;
};
```

### QResource 类（Qt兼容接口）

```cpp
class QResource {
public:
    explicit QResource(const std::string& path);
    
    bool isValid() const;
    const unsigned char* data() const;
    size_t size() const;
    std::string path() const;
};
```

### 便利函数

```cpp
namespace ResourceSystem {
    std::string loadResourceAsString(const std::string& path);
    std::vector<unsigned char> loadResourceAsBytes(const std::string& path);
    bool saveResourceToFile(const std::string& resourcePath, const std::string& filePath);
}
```

## 构建和运行示例

```bash
# 配置和构建
mkdir build && cd build
cmake ..
make

# 运行资源系统示例
./bin/ResourceExample
```

## 与Qt QRC的对比

| 功能 | Qt QRC | 本实现 |
|------|--------|--------|
| 文件嵌入 | ✅ | ✅ |
| 路径别名 | ✅ | ✅ |
| 目录结构 | ✅ | ✅ |
| 二进制数据 | ✅ | ✅ |
| 压缩支持 | ✅ | ❌ |
| 加密支持 | ❌ | ❌ |
| 运行时加载 | ✅ | ❌ |
| 跨平台 | ✅ | ✅ |

## 注意事项

1. **内存使用**：所有资源都会加载到内存中，大文件会增加程序内存占用
2. **编译时间**：大量资源会增加编译时间
3. **Python依赖**：构建时需要Python3环境
4. **路径格式**：资源路径统一使用Unix风格的正斜杠

## 扩展功能

可以考虑添加的功能：
- 资源压缩（zlib/gzip）
- 资源加密
- 延迟加载
- 资源热重载（开发模式）
- 多线程安全访问
