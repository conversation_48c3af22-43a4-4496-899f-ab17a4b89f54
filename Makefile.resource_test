# 简单的Makefile用于测试资源系统
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -g

# 目标文件
TARGETS = simple_test

# 源文件
RESOURCE_SOURCES = resource_system.cpp
TEST_SOURCES = simple_test.cpp

all: $(TARGETS)

simple_test: $(TEST_SOURCES) $(RESOURCE_SOURCES)
	$(CXX) $(CXXFLAGS) -o $@ $^

# 生成资源文件（如果有Python）
generated_resources.cpp: example_resources.json resource_generator.py
	python3 resource_generator.py example_resources.json generated_resources.cpp

# 使用生成的资源的示例
resource_example: resource_example.cpp $(RESOURCE_SOURCES) generated_resources.cpp
	$(CXX) $(CXXFLAGS) -o $@ $^

clean:
	rm -f $(TARGETS) resource_example generated_resources.cpp extracted_*.txt

test: simple_test
	./simple_test

.PHONY: all clean test
