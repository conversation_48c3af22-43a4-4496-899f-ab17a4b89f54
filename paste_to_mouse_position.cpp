// 修改后的复制粘贴代码，支持粘贴到鼠标指定位置
#include <QApplication>
#include <QCursor>
#include <QWidget>
#include <QRect>
#include <QPoint>
#include <QClipboard>

// 复制功能 (Ctrl+C) - 保持不变
if (keyEvent->key() == Qt::Key_C)
{
    if (m_pPageManager->getCurSelectPage() != m_root_host) return false;

    if (m_selection->selectedWidgets().size() > 0)
    {
        m_originalCenters.clear();  // 清空之前的记录
        XMLObject xmlObj;
        xmlObj.setTagName("hosts");

        // 获取当前选中控件的最高父类
        CMvAbstractHost* targetParentHost = nullptr;
        foreach(QWidget * widgetObj, m_selection->selectedWidgets())
        {
            CMvAbstractHost* host = m_widget_to_host.value(widgetObj);
            if (!targetParentHost || (host && host->getParent()== m_root_host))
            {
                targetParentHost = host->getParent();
            }
            else if (host && host->getParent() != m_root_host)
            {
                // 向上查找最高父类
                CMvAbstractHost* parent = host->getParent();
                while (parent && parent->getParent() != m_root_host)
                {
                    parent = parent->getParent();
                }
                if (parent)
                {
                    targetParentHost = parent;
                }
            }

            XMLObject* pChildObj = new XMLObject(&xmlObj);
            host->toObject(pChildObj);

            // 记录每个控件的中心位置
            const QRect re = host->getPropertyValue("geometry").toRect();
            m_originalCenters.append(re.center());
        }

        // 保存目标父类
        m_currentPasteParent = targetParentHost ? targetParentHost : m_root_host;

        // 重置粘贴状态
        m_lastPastePositions = m_originalCenters; // 初始化为原始位置
        m_isFirstPasteAfterCopy = true;

        QApplication::clipboard()->setText(xmlObj.write());
        qApp->setProperty("pos", m_originalCenters.first()); // 保存第一个位置
    }
}
// 粘贴功能 (Ctrl+V) - 修改为粘贴到鼠标位置
else if (keyEvent->key() == Qt::Key_V)
{
    if (m_pPageManager->getCurSelectPage() != m_root_host) return false;

    XMLObject xmlObj;
    QString copyDat = QApplication::clipboard()->text();

    if (!xmlObj.load(copyDat, nullptr))
    {
        return false;
    }

    bool hasValidHosts = false;
    QUndoCommand* cmd = new QUndoCommand;

    // 获取目标父类
    CMvAbstractHost* targetParent = m_currentPasteParent ? m_currentPasteParent : m_root_host;

    // 获取鼠标当前位置（全局坐标）
    QPoint globalMousePos = QCursor::pos();
    
    // 将全局坐标转换为目标父控件的本地坐标
    // 注意：这里假设您有方法获取CMvAbstractHost对应的QWidget
    QWidget* targetWidget = nullptr;
    if (targetParent)
    {
        // 根据您的实际代码结构，可能需要调整获取widget的方法
        // 例如：targetWidget = targetParent->getWidget();
        // 或者：targetWidget = targetParent->widget();
        // 这里使用一个通用的方法名，您需要根据实际情况调整
        targetWidget = targetParent->getAssociatedWidget(); // 请根据实际API调整
    }
    
    QPoint localMousePos;
    if (targetWidget)
    {
        localMousePos = targetWidget->mapFromGlobal(globalMousePos);
    }
    else
    {
        // 如果无法获取目标widget，使用全局坐标
        localMousePos = globalMousePos;
    }

    // 获取复制的控件列表
    QList<int> index;
    QList<CMvAbstractHost*> list;
    QList<XMLObject*> xmlHosts = xmlObj.getChildren();
    
    // 计算所有控件的边界框，用于确定相对位置
    QRect boundingRect;
    QList<QRect> originalGeometries;
    
    // 首先收集所有原始几何信息
    for (int i = 0; i < xmlHosts.size() && i < m_originalCenters.size(); ++i)
    {
        XMLObject* xmlHost = xmlHosts[i];
        CMvAbstractHost* pHostObj = CMvHostFactory::createHost(xmlHost);
        if (pHostObj != nullptr)
        {
            pHostObj->generateLayoutInAdvance(xmlHost);
            pHostObj->fromObject(xmlHost);
            
            QRect geometry = pHostObj->getPropertyValue("geometry").toRect();
            originalGeometries.append(geometry);
            
            if (i == 0)
            {
                boundingRect = geometry;
            }
            else
            {
                boundingRect = boundingRect.united(geometry);
            }
            
            delete pHostObj; // 临时创建，用完即删
        }
    }
    
    // 计算偏移量：鼠标位置 - 边界框中心
    QPoint offset = localMousePos - boundingRect.center();
    
    // 如果不是第一次粘贴，添加额外的偏移避免重叠
    if (!m_isFirstPasteAfterCopy)
    {
        offset += QPoint(20, 20);
    }

    // 重新创建控件并设置新位置
    for (int i = 0; i < xmlHosts.size() && i < originalGeometries.size(); ++i)
    {
        XMLObject* xmlHost = xmlHosts[i];
        CMvAbstractHost* pHostObj = CMvHostFactory::createHost(xmlHost);
        if (pHostObj != nullptr)
        {
            pHostObj->generateLayoutInAdvance(xmlHost);
            pHostObj->fromObject(xmlHost);
            pHostObj->setDefault();
            pHostObj->setId(targetParent, m_pPageManager);
            
            // 计算新位置：原始位置 + 偏移量
            QRect newGeometry = originalGeometries[i];
            newGeometry.translate(offset);
            
            // 确保控件不会超出父控件边界（可选）
            if (targetWidget)
            {
                QRect parentRect = targetWidget->rect();
                if (newGeometry.right() > parentRect.right())
                {
                    newGeometry.moveRight(parentRect.right());
                }
                if (newGeometry.bottom() > parentRect.bottom())
                {
                    newGeometry.moveBottom(parentRect.bottom());
                }
                if (newGeometry.left() < parentRect.left())
                {
                    newGeometry.moveLeft(parentRect.left());
                }
                if (newGeometry.top() < parentRect.top())
                {
                    newGeometry.moveTop(parentRect.top());
                }
            }
            
            pHostObj->setPropertyValue("geometry", newGeometry);

            // 更新最后粘贴位置（使用控件中心位置）
            if (i < m_lastPastePositions.size())
            {
                m_lastPastePositions[i] = newGeometry.center();
            }
            else
            {
                m_lastPastePositions.append(newGeometry.center());
            }

            list.append(pHostObj);
            index.append(targetParent->getChildCount() + i);

            hasValidHosts = true;
        }
    }

    // 第一次粘贴完成后标记
    if (hasValidHosts && m_isFirstPasteAfterCopy)
    {
        m_isFirstPasteAfterCopy = false;
    }

    if (hasValidHosts)
    {
        new CMvAddHostUndoCommand(m_pPageManager, targetParent, list, index, AHT_ADD, cmd);
        m_pUndoStack->push(cmd);

        QList<CMvAbstractHost*> allHost;
        for (auto host : list)
        {
            host->getAllDescendants(allHost);
        }

        for (auto host : allHost)
        {
            QString dataBind = host->getPropertyValue("dataBind").toString();
            if (!dataBind.isEmpty())
            {
                QVariant var(dataBind);
                host->generateEventCode("dataBind", var, m_pPageManager, false);
            }

            QString suffix = "";
            QStringList eventList;
            host->getEventPropertyName(suffix, eventList);
            for (auto str : eventList)
            {
                QString element = host->getPropertyValue(str).toString();
                if (!element.isEmpty())
                {
                    QVariant var(element);
                    host->generateEventCode(str, var, m_pPageManager, false);
                }
            }
        }
    }
}
