#include <iostream>
#include "resource_system.h"

// 这个文件需要在编译时由resource_generator.py生成
// 例如: python resource_generator.py example_resources.json generated_resources.cpp
// 然后在CMakeLists.txt中包含generated_resources.cpp

int main() {
    std::cout << "=== Resource System Demo ===" << std::endl;
    
    // 获取资源管理器实例
    auto& rm = ResourceSystem::ResourceManager::instance();
    
    // 列出所有资源
    std::cout << "\nAvailable resources:" << std::endl;
    auto resources = rm.listResources();
    for (const auto& path : resources) {
        std::cout << "  " << path << std::endl;
    }
    
    // 测试资源访问
    std::string readmePath = "/docs/readme.md";
    if (rm.hasResource(readmePath)) {
        std::cout << "\n=== Content of " << readmePath << " ===" << std::endl;
        std::string content = rm.getResourceAsString(readmePath);
        std::cout << content.substr(0, 200) << "..." << std::endl;
        
        // 保存到文件
        if (rm.saveResourceToFile(readmePath, "extracted_readme.md")) {
            std::cout << "Resource saved to extracted_readme.md" << std::endl;
        }
    } else {
        std::cout << "Resource not found: " << readmePath << std::endl;
    }
    
    // 使用QResource类（模拟Qt接口）
    std::cout << "\n=== Using QResource interface ===" << std::endl;
    QResource resource("/config/cmake.txt");
    if (resource.isValid()) {
        std::cout << "Resource path: " << resource.path() << std::endl;
        std::cout << "Resource size: " << resource.size() << " bytes" << std::endl;
        
        // 显示前100个字符
        std::string content(reinterpret_cast<const char*>(resource.data()), 
                          std::min(resource.size(), size_t(100)));
        std::cout << "Content preview: " << content << "..." << std::endl;
    }
    
    // 使用便利函数
    std::cout << "\n=== Using convenience functions ===" << std::endl;
    std::string cmakeContent = ResourceSystem::loadResourceAsString("/config/cmake.txt");
    if (!cmakeContent.empty()) {
        std::cout << "CMake file loaded, size: " << cmakeContent.size() << " bytes" << std::endl;
    }
    
    return 0;
}
