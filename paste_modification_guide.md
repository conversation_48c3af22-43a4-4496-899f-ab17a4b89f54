# 粘贴到鼠标位置功能修改指南

## 概述
将原有的基于固定偏移的粘贴功能修改为基于鼠标当前位置的粘贴功能。

## 主要修改点

### 1. 获取鼠标位置
```cpp
// 获取鼠标当前位置（全局坐标）
QPoint globalMousePos = QCursor::pos();

// 将全局坐标转换为目标父控件的本地坐标
QWidget* targetWidget = /* 获取目标控件的方法 */;
QPoint localMousePos = targetWidget->mapFromGlobal(globalMousePos);
```

### 2. 计算控件组的边界框
```cpp
// 计算所有选中控件的边界框
QRect boundingRect;
for (int i = 0; i < m_originalCenters.size(); ++i)
{
    QPoint center = m_originalCenters[i];
    QRect rect(center.x() - 50, center.y() - 25, 100, 50); // 估算大小
    
    if (i == 0)
        boundingRect = rect;
    else
        boundingRect = boundingRect.united(rect);
}
```

### 3. 计算偏移量
```cpp
// 计算偏移量：鼠标位置 - 边界框中心
QPoint mouseOffset = localMousePos - boundingRect.center();

// 如果不是第一次粘贴，添加额外偏移避免重叠
if (!m_isFirstPasteAfterCopy)
{
    mouseOffset += QPoint(20, 20);
}
```

### 4. 应用新位置
```cpp
// 对每个控件应用偏移
QRect newGeometry = originalGeometry;
newGeometry.translate(mouseOffset);

// 可选：边界检查
if (targetWidget)
{
    QRect parentRect = targetWidget->rect();
    // 确保控件在父控件范围内
    if (newGeometry.right() > parentRect.right())
        newGeometry.moveRight(parentRect.right());
    // ... 其他边界检查
}

pHostObj->setPropertyValue("geometry", newGeometry);
```

## 需要适配的部分

### 1. 获取目标Widget
根据您的代码结构，可能需要以下方式之一：

```cpp
// 方式1：直接方法
QWidget* targetWidget = targetParent->getWidget();

// 方式2：通过映射查找
foreach(QWidget* widget, m_widget_to_host.keys())
{
    if (m_widget_to_host.value(widget) == targetParent)
    {
        targetWidget = widget;
        break;
    }
}

// 方式3：特定API
QWidget* targetWidget = targetParent->getAssociatedWidget();
```

### 2. 坐标系统
确保坐标转换正确：
- 全局坐标：屏幕坐标系
- 本地坐标：相对于父控件的坐标系

### 3. 边界框计算
如果能获取实际的控件几何信息，使用实际大小：
```cpp
// 更精确的边界框计算
QRect geometry = pHostObj->getPropertyValue("geometry").toRect();
if (i == 0)
    boundingRect = geometry;
else
    boundingRect = boundingRect.united(geometry);
```

## 优化建议

### 1. 性能优化
- 缓存目标Widget的查找结果
- 避免重复的坐标转换计算

### 2. 用户体验
- 添加视觉反馈，显示粘贴预览
- 支持撤销/重做操作
- 智能避免控件重叠

### 3. 边界处理
- 当鼠标在父控件外时的处理策略
- 控件部分超出边界时的调整策略

## 测试要点

1. **基本功能**
   - 复制单个控件，粘贴到鼠标位置
   - 复制多个控件，保持相对位置关系

2. **边界情况**
   - 鼠标在父控件边缘时的粘贴
   - 鼠标在父控件外时的粘贴
   - 连续多次粘贴的位置递增

3. **坐标系统**
   - 不同缩放比例下的坐标转换
   - 多显示器环境下的坐标处理

## 完整的修改流程

1. **备份原始代码**
2. **修改粘贴逻辑**
   - 替换位置计算部分
   - 添加鼠标位置获取
   - 实现坐标转换
3. **测试验证**
   - 单元测试
   - 集成测试
   - 用户体验测试
4. **优化调整**
   - 性能优化
   - 边界处理优化
   - 用户反馈处理

## 注意事项

1. **线程安全**：确保在主线程中获取鼠标位置
2. **内存管理**：注意临时创建的对象的生命周期
3. **异常处理**：处理坐标转换可能的异常情况
4. **兼容性**：确保修改不影响其他功能

这个修改将使粘贴功能更加直观和用户友好，用户可以精确控制粘贴位置。
