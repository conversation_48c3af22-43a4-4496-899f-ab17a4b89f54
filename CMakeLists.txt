cmake_minimum_required(VERSION 3.16)
project(TabHidingTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt
find_package(Qt6 COMPONENTS Core Widgets QUIET)
if(NOT Qt6_FOUND)
    find_package(Qt5 COMPONENTS Core Widgets REQUIRED)
    set(QT_VERSION_MAJOR 5)
else()
    set(QT_VERSION_MAJOR 6)
endif()

# 设置Qt相关设置
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 资源系统相关文件
set(RESOURCE_SYSTEM_SOURCES
    resource_system.cpp
)

# 生成资源文件的自定义命令
set(RESOURCE_CONFIG "${CMAKE_CURRENT_SOURCE_DIR}/example_resources.json")
set(GENERATED_RESOURCES "${CMAKE_CURRENT_BINARY_DIR}/generated_resources.cpp")

add_custom_command(
    OUTPUT ${GENERATED_RESOURCES}
    COMMAND ${CMAKE_COMMAND} -E env python3 "${CMAKE_CURRENT_SOURCE_DIR}/resource_generator.py"
            "${RESOURCE_CONFIG}" "${GENERATED_RESOURCES}"
    DEPENDS ${RESOURCE_CONFIG} "${CMAKE_CURRENT_SOURCE_DIR}/resource_generator.py"
    COMMENT "Generating embedded resources"
    VERBATIM
)

# 源文件
set(COMMON_SOURCES
    CMvTabWidgetExe_fixed.cpp
    CustomTabBar.cpp
)

set(BASIC_TEST_SOURCES
    ${COMMON_SOURCES}
    test_tab_hiding.cpp
)

set(DYNAMIC_TEST_SOURCES
    ${COMMON_SOURCES}
    dynamic_tab_test.cpp
)

set(USAGE_EXAMPLE_SOURCES
    ${COMMON_SOURCES}
    dynamic_usage_example.cpp
)

# 头文件
set(HEADERS
    CMvTabWidgetExe.h
    CustomTabBar.h
    resource_system.h
)

# 创建多个可执行文件
add_executable(BasicTabTest ${BASIC_TEST_SOURCES} ${HEADERS})
add_executable(DynamicTabTest ${DYNAMIC_TEST_SOURCES} ${HEADERS})
add_executable(UsageExample ${USAGE_EXAMPLE_SOURCES} ${HEADERS})

# 添加资源系统示例
add_executable(ResourceExample
    resource_example.cpp
    ${RESOURCE_SYSTEM_SOURCES}
    ${GENERATED_RESOURCES}
    ${HEADERS}
)

# 链接Qt库
set(TARGETS BasicTabTest DynamicTabTest UsageExample ResourceExample)

foreach(TARGET ${TARGETS})
    if(QT_VERSION_MAJOR EQUAL 6)
        target_link_libraries(${TARGET} Qt6::Core Qt6::Widgets)
    else()
        target_link_libraries(${TARGET} Qt5::Core Qt5::Widgets)
    endif()

    # 设置输出目录
    set_target_properties(${TARGET} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )

    # 编译选项
    if(MSVC)
        target_compile_options(${TARGET} PRIVATE /W3)
    else()
        target_compile_options(${TARGET} PRIVATE -Wall -Wextra)
    endif()
endforeach()

# 如果是Windows，复制Qt DLL
if(WIN32 AND QT_VERSION_MAJOR EQUAL 6)
    # Qt6的windeployqt
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)
elseif(WIN32 AND QT_VERSION_MAJOR EQUAL 5)
    # Qt5的windeployqt
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt5_DIR}/../../../bin)
endif()

if(WIN32 AND WINDEPLOYQT_EXECUTABLE)
    foreach(TARGET ${TARGETS})
        add_custom_command(TARGET ${TARGET} POST_BUILD
            COMMAND ${WINDEPLOYQT_EXECUTABLE} $<TARGET_FILE:${TARGET}>
            COMMENT "Deploying Qt libraries for ${TARGET}")
    endforeach()
endif()

# 打印配置信息
message(STATUS "Qt version: ${QT_VERSION_MAJOR}")
message(STATUS "CMake version: ${CMAKE_VERSION}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
