// 只需要修改的关键部分 - 替换您代码中的相应部分

// 原来的边界框计算部分，替换为：
// 计算基于鼠标位置的偏移 ==========
// 找到所有控件的最左上角位置
QPoint topLeftMost;
bool firstPoint = true;

// 先遍历一次，找到控件组的最左上角
for (int i = 0; i < xmlHosts.size(); ++i)
{
    XMLObject* xmlHost = xmlHosts[i];
    CMvAbstractHost* tempHost = CMvHostFactory::createHost(xmlHost);
    if (tempHost != nullptr)
    {
        tempHost->generateLayoutInAdvance(xmlHost);
        tempHost->fromObject(xmlHost);
        
        QRect geometry = tempHost->getPropertyValue("geometry").toRect();
        QPoint topLeft = geometry.topLeft();
        
        if (firstPoint)
        {
            topLeftMost = topLeft;
            firstPoint = false;
        }
        else
        {
            // 找到最左上角的点
            if (topLeft.x() < topLeftMost.x())
                topLeftMost.setX(topLeft.x());
            if (topLeft.y() < topLeftMost.y())
                topLeftMost.setY(topLeft.y());
        }
        
        delete tempHost; // 临时对象，用完即删
    }
}

// 计算偏移量：鼠标位置 - 控件组的最左上角
QPoint mouseOffset = localMousePos - topLeftMost;

// 如果不是第一次粘贴，添加额外偏移避免重叠
if (!m_isFirstPasteAfterCopy)
{
    mouseOffset += QPoint(20, 20);
}

/*
具体替换说明：

在您的代码中，找到这部分：
```cpp
// 计算基于鼠标位置的偏移 ==========
// 计算所有控件的边界框
QRect boundingRect;
bool firstRect = true;

for (int i = 0; i < xmlHosts.size() && i < m_originalCenters.size(); ++i)
{
    if (i < m_originalCenters.size())
    {
        QPoint center = m_originalCenters[i];
        QRect rect(center.x() - 50, center.y() - 25, 100, 50); // 假设默认大小

        if (firstRect)
        {
            boundingRect = rect;
            firstRect = false;
        }
        else
        {
            boundingRect = boundingRect.united(rect);
        }
    }
}

// 计算偏移量：鼠标位置 - 边界框中心
QPoint mouseOffset = localMousePos - boundingRect.center();
```

替换为上面的新代码即可。

主要变化：
1. 不再计算边界框的中心
2. 直接找到所有控件的最左上角点
3. 偏移量 = 鼠标位置 - 最左上角点
4. 这样控件组的左上角就会定位到鼠标位置
*/
