#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QSpinBox>
#include <QLabel>
#include <QTextEdit>
#include <QComboBox>
#include "CMvTabWidgetExe.h"

class TestWindow : public QMainWindow
{
    Q_OBJECT

public:
    TestWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        setupConnections();
    }

private slots:
    void onLevelChanged(int level)
    {
        QString method = methodCombo->currentText();
        
        if (method == "Simple Method (Recommended)")
        {
            tabWidget->handleLevelSimple(level);
        }
        else if (method == "Remove/Insert Method")
        {
            tabWidget->handleLevel(level);
        }
        else if (method == "StyleSheet Method")
        {
            tabWidget->handleLevelStyleSheet(level);
        }
        else if (method == "Custom TabBar Method")
        {
            tabWidget->handleLevelCustomTabBar(level);
        }
        
        updateStatus();
    }
    
    void onMethodChanged()
    {
        onLevelChanged(levelSpinBox->value());
    }

private:
    void setupUI()
    {
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 控制面板
        QHBoxLayout* controlLayout = new QHBoxLayout();
        
        controlLayout->addWidget(new QLabel("User Level:"));
        levelSpinBox = new QSpinBox();
        levelSpinBox->setRange(1, 10);
        levelSpinBox->setValue(5);
        controlLayout->addWidget(levelSpinBox);
        
        controlLayout->addWidget(new QLabel("Method:"));
        methodCombo = new QComboBox();
        methodCombo->addItems({
            "Simple Method (Recommended)",
            "Remove/Insert Method", 
            "StyleSheet Method",
            "Custom TabBar Method"
        });
        controlLayout->addWidget(methodCombo);
        
        controlLayout->addStretch();
        mainLayout->addLayout(controlLayout);
        
        // 标签页控件
        tabWidget = new CMvTabWidgetExe();
        setupTabs();
        mainLayout->addWidget(tabWidget);
        
        // 状态显示
        statusText = new QTextEdit();
        statusText->setMaximumHeight(100);
        statusText->setReadOnly(true);
        mainLayout->addWidget(statusText);
        
        setWindowTitle("Tab Hiding Test - Qt Compatible Version");
        resize(600, 400);
    }
    
    void setupTabs()
    {
        // 创建测试标签页
        QWidget* tab1 = new QWidget();
        QVBoxLayout* layout1 = new QVBoxLayout(tab1);
        layout1->addWidget(new QLabel("Basic User Content\n(Visible at level 1+)"));
        tabWidget->addTab(tab1, "Basic");
        
        QWidget* tab2 = new QWidget();
        QVBoxLayout* layout2 = new QVBoxLayout(tab2);
        layout2->addWidget(new QLabel("Advanced User Content\n(Visible at level 5+)"));
        tabWidget->addTab(tab2, "Advanced");
        
        QWidget* tab3 = new QWidget();
        QVBoxLayout* layout3 = new QVBoxLayout(tab3);
        layout3->addWidget(new QLabel("Admin Content\n(Visible at level 8+)"));
        tabWidget->addTab(tab3, "Admin");
        
        QWidget* tab4 = new QWidget();
        QVBoxLayout* layout4 = new QVBoxLayout(tab4);
        layout4->addWidget(new QLabel("Super Admin Content\n(Visible at level 10+)"));
        tabWidget->addTab(tab4, "Super Admin");
        
        // 设置权限级别信息
        // 格式：可见级别-启用级别$-可见级别-启用级别$-...
        tabWidget->setProperty("allTabLevelInfo", "1-1$-5-3$-8-8$-10-10");
    }
    
    void setupConnections()
    {
        connect(levelSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                this, &TestWindow::onLevelChanged);
        connect(methodCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
                this, &TestWindow::onMethodChanged);
    }
    
    void updateStatus()
    {
        QString status = QString("Current Level: %1\n").arg(levelSpinBox->value());
        status += QString("Method: %1\n").arg(methodCombo->currentText());
        status += "Tab Status:\n";
        
        QStringList levelInfo = tabWidget->property("allTabLevelInfo").toString().split("$-");
        QStringList tabNames = {"Basic", "Advanced", "Admin", "Super Admin"};
        
        int currentLevel = levelSpinBox->value();
        
        for (int i = 0; i < levelInfo.size() && i < tabNames.size(); ++i)
        {
            QStringList levels = levelInfo[i].split("-");
            if (levels.size() == 2)
            {
                int visibleLevel = levels[0].toInt();
                int enabledLevel = levels[1].toInt();
                
                bool visible = (currentLevel >= visibleLevel);
                bool enabled = (currentLevel >= enabledLevel);
                
                QString tabStatus;
                if (!visible)
                    tabStatus = "Hidden";
                else if (!enabled)
                    tabStatus = "Visible but Disabled";
                else
                    tabStatus = "Visible and Enabled";
                    
                status += QString("  %1: %2\n").arg(tabNames[i], tabStatus);
            }
        }
        
        statusText->setText(status);
    }

private:
    CMvTabWidgetExe* tabWidget;
    QSpinBox* levelSpinBox;
    QComboBox* methodCombo;
    QTextEdit* statusText;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestWindow window;
    window.show();
    
    return app.exec();
}

#include "test_tab_hiding.moc"
