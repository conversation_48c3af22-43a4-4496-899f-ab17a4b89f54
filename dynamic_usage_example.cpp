// 动态标签页隐藏/显示的实际使用示例

#include "CMvTabWidgetExe.h"
#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>

class UserManagementSystem : public QMainWindow
{
    Q_OBJECT

public:
    UserManagementSystem(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        
        // 模拟用户登录，初始权限级别为1（普通用户）
        currentUserLevel = 1;
        updateUserPermissions();
    }

private slots:
    // 模拟用户权限升级
    void promoteUser()
    {
        if (currentUserLevel < 10)
        {
            currentUserLevel++;
            updateUserPermissions();
            
            // 显示权限变化通知
            statusLabel->setText(QString("用户权限已升级到级别 %1").arg(currentUserLevel));
        }
    }
    
    // 模拟用户权限降级
    void demoteUser()
    {
        if (currentUserLevel > 1)
        {
            currentUserLevel--;
            updateUserPermissions();
            
            statusLabel->setText(QString("用户权限已降级到级别 %1").arg(currentUserLevel));
        }
    }
    
    // 模拟用户登出/登入不同权限的用户
    void switchUser()
    {
        // 模拟切换到不同权限级别的用户
        static int userTypes[] = {1, 3, 5, 8, 10}; // 不同类型用户的权限级别
        static int currentUserType = 0;
        
        currentUserType = (currentUserType + 1) % 5;
        currentUserLevel = userTypes[currentUserType];
        
        updateUserPermissions();
        
        QString userTypeName;
        switch (currentUserLevel)
        {
            case 1: userTypeName = "普通用户"; break;
            case 3: userTypeName = "注册用户"; break;
            case 5: userTypeName = "VIP用户"; break;
            case 8: userTypeName = "管理员"; break;
            case 10: userTypeName = "超级管理员"; break;
        }
        
        statusLabel->setText(QString("已切换到 %1 (级别 %2)").arg(userTypeName).arg(currentUserLevel));
    }

private:
    void setupUI()
    {
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 控制按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        
        QPushButton* promoteBtn = new QPushButton("权限升级");
        QPushButton* demoteBtn = new QPushButton("权限降级");
        QPushButton* switchBtn = new QPushButton("切换用户");
        
        buttonLayout->addWidget(promoteBtn);
        buttonLayout->addWidget(demoteBtn);
        buttonLayout->addWidget(switchBtn);
        buttonLayout->addStretch();
        
        mainLayout->addLayout(buttonLayout);
        
        // 状态标签
        statusLabel = new QLabel("当前用户：普通用户 (级别 1)");
        mainLayout->addWidget(statusLabel);
        
        // 主要的标签页控件
        tabWidget = new CMvTabWidgetExe();
        setupTabs();
        mainLayout->addWidget(tabWidget);
        
        // 连接信号
        connect(promoteBtn, &QPushButton::clicked, this, &UserManagementSystem::promoteUser);
        connect(demoteBtn, &QPushButton::clicked, this, &UserManagementSystem::demoteUser);
        connect(switchBtn, &QPushButton::clicked, this, &UserManagementSystem::switchUser);
        
        setWindowTitle("用户权限管理系统 - 动态标签页演示");
        resize(800, 600);
    }
    
    void setupTabs()
    {
        // 1. 基础功能标签页 (级别1可见)
        QWidget* basicTab = new QWidget();
        QVBoxLayout* basicLayout = new QVBoxLayout(basicTab);
        basicLayout->addWidget(new QLabel("基础功能"));
        basicLayout->addWidget(new QLabel("• 查看个人信息"));
        basicLayout->addWidget(new QLabel("• 修改密码"));
        basicLayout->addWidget(new QLabel("• 基本设置"));
        tabWidget->addTab(basicTab, "基础功能");
        
        // 2. 用户功能标签页 (级别3可见)
        QWidget* userTab = new QWidget();
        QVBoxLayout* userLayout = new QVBoxLayout(userTab);
        userLayout->addWidget(new QLabel("注册用户功能"));
        userLayout->addWidget(new QLabel("• 创建内容"));
        userLayout->addWidget(new QLabel("• 评论和互动"));
        userLayout->addWidget(new QLabel("• 个人收藏"));
        tabWidget->addTab(userTab, "用户功能");
        
        // 3. VIP功能标签页 (级别5可见)
        QWidget* vipTab = new QWidget();
        QVBoxLayout* vipLayout = new QVBoxLayout(vipTab);
        vipLayout->addWidget(new QLabel("VIP专享功能"));
        vipLayout->addWidget(new QLabel("• 高级搜索"));
        vipLayout->addWidget(new QLabel("• 数据导出"));
        vipLayout->addWidget(new QLabel("• 优先客服"));
        tabWidget->addTab(vipTab, "VIP功能");
        
        // 4. 内容管理标签页 (级别6可见)
        QWidget* contentTab = new QWidget();
        QVBoxLayout* contentLayout = new QVBoxLayout(contentTab);
        contentLayout->addWidget(new QLabel("内容管理"));
        contentLayout->addWidget(new QLabel("• 审核内容"));
        contentLayout->addWidget(new QLabel("• 管理评论"));
        contentLayout->addWidget(new QLabel("• 内容统计"));
        tabWidget->addTab(contentTab, "内容管理");
        
        // 5. 用户管理标签页 (级别8可见)
        QWidget* userMgmtTab = new QWidget();
        QVBoxLayout* userMgmtLayout = new QVBoxLayout(userMgmtTab);
        userMgmtLayout->addWidget(new QLabel("用户管理"));
        userMgmtLayout->addWidget(new QLabel("• 用户列表"));
        userMgmtLayout->addWidget(new QLabel("• 权限设置"));
        userMgmtLayout->addWidget(new QLabel("• 封禁管理"));
        tabWidget->addTab(userMgmtTab, "用户管理");
        
        // 6. 系统管理标签页 (级别10可见)
        QWidget* systemTab = new QWidget();
        QVBoxLayout* systemLayout = new QVBoxLayout(systemTab);
        systemLayout->addWidget(new QLabel("系统管理"));
        systemLayout->addWidget(new QLabel("• 系统配置"));
        systemLayout->addWidget(new QLabel("• 数据库管理"));
        systemLayout->addWidget(new QLabel("• 日志查看"));
        systemLayout->addWidget(new QLabel("• 备份恢复"));
        tabWidget->addTab(systemTab, "系统管理");
        
        // 设置权限级别信息
        // 格式：可见级别-启用级别$-可见级别-启用级别$-...
        tabWidget->setProperty("allTabLevelInfo", "1-1$-3-3$-5-5$-6-6$-8-8$-10-10");
    }
    
    void updateUserPermissions()
    {
        // 使用动态移除/插入方法更新标签页可见性
        tabWidget->handleLevelRemoveInsert(currentUserLevel);
        
        // 更新状态显示
        QString userType;
        switch (currentUserLevel)
        {
            case 1: userType = "普通用户"; break;
            case 2: userType = "普通用户"; break;
            case 3: userType = "注册用户"; break;
            case 4: userType = "注册用户"; break;
            case 5: userType = "VIP用户"; break;
            case 6: userType = "内容管理员"; break;
            case 7: userType = "内容管理员"; break;
            case 8: userType = "管理员"; break;
            case 9: userType = "管理员"; break;
            case 10: userType = "超级管理员"; break;
        }
        
        statusLabel->setText(QString("当前用户：%1 (级别 %2) - 可见标签页：%3个")
                           .arg(userType)
                           .arg(currentUserLevel)
                           .arg(tabWidget->count()));
    }

private:
    CMvTabWidgetExe* tabWidget;
    QLabel* statusLabel;
    int currentUserLevel;
};

// 使用示例的主要特点：
/*
1. 动态权限变化：
   - 用户权限可以实时升级或降级
   - 标签页会立即响应权限变化
   - 保持用户当前的操作状态

2. 多用户切换：
   - 支持不同权限级别用户的快速切换
   - 每次切换都会正确显示对应的标签页

3. 状态保持：
   - 使用方案1的改进版本，保持标签页的原始状态
   - 当权限恢复时，标签页会恢复到之前的状态
   - 保持用户的选中状态

4. 实际应用场景：
   - 用户管理系统
   - 权限分级的企业应用
   - 多角色的管理后台
   - 会员等级系统

5. 关键优势：
   - 完全隐藏不可见的标签页（包括标签头）
   - 支持频繁的权限变化
   - 保持良好的用户体验
   - 不会丢失标签页状态
*/

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    UserManagementSystem window;
    window.show();
    
    return app.exec();
}

#include "dynamic_usage_example.moc"
