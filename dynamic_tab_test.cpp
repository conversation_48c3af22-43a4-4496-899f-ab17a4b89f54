#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QSpinBox>
#include <QLabel>
#include <QTextEdit>
#include <QTimer>
#include <QRandomGenerator>
#include "CMvTabWidgetExe.h"

class DynamicTabTest : public QMainWindow
{
    Q_OBJECT

public:
    DynamicTabTest(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        setupConnections();
        
        // 启动自动权限变化演示
        setupAutoDemo();
    }

private slots:
    void onLevelChanged(int level)
    {
        // 使用动态移除/插入方法
        tabWidget->handleLevelRemoveInsert(level);
        updateStatus();
    }
    
    void onAutoDemo()
    {
        if (autoDemoEnabled)
        {
            // 随机生成1-10的权限级别
            int randomLevel = QRandomGenerator::global()->bounded(1, 11);
            levelSpinBox->setValue(randomLevel);
            
            statusText->append(QString("自动演示：权限级别变更为 %1").arg(randomLevel));
        }
    }
    
    void toggleAutoDemo()
    {
        autoDemoEnabled = !autoDemoEnabled;
        autoDemoButton->setText(autoDemoEnabled ? "停止自动演示" : "开始自动演示");
        
        if (autoDemoEnabled)
        {
            autoTimer->start(2000); // 每2秒变化一次
            statusText->append("开始自动权限变化演示...");
        }
        else
        {
            autoTimer->stop();
            statusText->append("停止自动演示");
        }
    }
    
    void addNewTab()
    {
        static int tabCounter = 5;
        
        QWidget* newTab = new QWidget();
        QVBoxLayout* layout = new QVBoxLayout(newTab);
        layout->addWidget(new QLabel(QString("动态添加的标签页 %1\n(权限级别: 3-3)").arg(tabCounter)));
        
        tabWidget->addTab(newTab, QString("动态Tab %1").arg(tabCounter));
        
        // 重置原始标签页信息，因为结构发生了变化
        tabWidget->resetOriginalTabs();
        
        // 更新权限信息
        QString currentInfo = tabWidget->property("allTabLevelInfo").toString();
        QString newInfo = currentInfo + "$-3-3";
        tabWidget->setProperty("allTabLevelInfo", newInfo);
        
        // 重新应用当前权限级别
        tabWidget->handleLevelRemoveInsert(levelSpinBox->value());
        
        tabCounter++;
        updateStatus();
    }
    
    void removeLastTab()
    {
        if (tabWidget->count() > 1) // 保留至少一个标签页
        {
            // 重置原始标签页信息
            tabWidget->resetOriginalTabs();
            
            // 移除最后一个标签页
            int lastIndex = tabWidget->count() - 1;
            QWidget* widget = tabWidget->widget(lastIndex);
            tabWidget->removeTab(lastIndex);
            delete widget;
            
            // 更新权限信息（移除最后一个）
            QString currentInfo = tabWidget->property("allTabLevelInfo").toString();
            QStringList parts = currentInfo.split("$-");
            if (parts.size() > 1)
            {
                parts.removeLast();
                tabWidget->setProperty("allTabLevelInfo", parts.join("$-"));
            }
            
            // 重新应用当前权限级别
            tabWidget->handleLevelRemoveInsert(levelSpinBox->value());
            
            updateStatus();
        }
    }

private:
    void setupUI()
    {
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 控制面板
        QHBoxLayout* controlLayout = new QHBoxLayout();
        
        controlLayout->addWidget(new QLabel("用户权限级别:"));
        levelSpinBox = new QSpinBox();
        levelSpinBox->setRange(1, 10);
        levelSpinBox->setValue(5);
        controlLayout->addWidget(levelSpinBox);
        
        autoDemoButton = new QPushButton("开始自动演示");
        controlLayout->addWidget(autoDemoButton);
        
        QPushButton* addTabButton = new QPushButton("添加标签页");
        controlLayout->addWidget(addTabButton);
        
        QPushButton* removeTabButton = new QPushButton("移除标签页");
        controlLayout->addWidget(removeTabButton);
        
        controlLayout->addStretch();
        mainLayout->addLayout(controlLayout);
        
        // 标签页控件
        tabWidget = new CMvTabWidgetExe();
        setupTabs();
        mainLayout->addWidget(tabWidget);
        
        // 状态显示
        statusText = new QTextEdit();
        statusText->setMaximumHeight(150);
        statusText->setReadOnly(true);
        mainLayout->addWidget(statusText);
        
        setWindowTitle("动态标签页隐藏/显示测试 - 方案1");
        resize(700, 500);
        
        // 连接信号
        connect(addTabButton, &QPushButton::clicked, this, &DynamicTabTest::addNewTab);
        connect(removeTabButton, &QPushButton::clicked, this, &DynamicTabTest::removeLastTab);
    }
    
    void setupTabs()
    {
        // 创建测试标签页
        QWidget* tab1 = new QWidget();
        QVBoxLayout* layout1 = new QVBoxLayout(tab1);
        layout1->addWidget(new QLabel("基础用户功能\n(权限级别: 1-1)\n任何用户都可以看到"));
        tabWidget->addTab(tab1, "基础");
        
        QWidget* tab2 = new QWidget();
        QVBoxLayout* layout2 = new QVBoxLayout(tab2);
        layout2->addWidget(new QLabel("中级用户功能\n(权限级别: 3-3)\n级别3+用户可见"));
        tabWidget->addTab(tab2, "中级");
        
        QWidget* tab3 = new QWidget();
        QVBoxLayout* layout3 = new QVBoxLayout(tab3);
        layout3->addWidget(new QLabel("高级用户功能\n(权限级别: 6-6)\n级别6+用户可见"));
        tabWidget->addTab(tab3, "高级");
        
        QWidget* tab4 = new QWidget();
        QVBoxLayout* layout4 = new QVBoxLayout(tab4);
        layout4->addWidget(new QLabel("管理员功能\n(权限级别: 9-9)\n级别9+用户可见"));
        tabWidget->addTab(tab4, "管理员");
        
        // 设置权限级别信息
        tabWidget->setProperty("allTabLevelInfo", "1-1$-3-3$-6-6$-9-9");
    }
    
    void setupConnections()
    {
        connect(levelSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
                this, &DynamicTabTest::onLevelChanged);
        connect(autoDemoButton, &QPushButton::clicked, this, &DynamicTabTest::toggleAutoDemo);
    }
    
    void setupAutoDemo()
    {
        autoTimer = new QTimer(this);
        connect(autoTimer, &QTimer::timeout, this, &DynamicTabTest::onAutoDemo);
        autoDemoEnabled = false;
    }
    
    void updateStatus()
    {
        QString status = QString("当前权限级别: %1\n").arg(levelSpinBox->value());
        status += QString("可见标签页数量: %1\n").arg(tabWidget->count());
        status += "标签页状态:\n";
        
        QStringList levelInfo = tabWidget->property("allTabLevelInfo").toString().split("$-");
        int currentLevel = levelSpinBox->value();
        
        for (int i = 0; i < levelInfo.size(); ++i)
        {
            QStringList levels = levelInfo[i].split("-");
            if (levels.size() == 2)
            {
                int visibleLevel = levels[0].toInt();
                int enabledLevel = levels[1].toInt();
                
                bool visible = (currentLevel >= visibleLevel);
                bool enabled = (currentLevel >= enabledLevel);
                
                QString tabStatus;
                if (!visible)
                    tabStatus = "隐藏";
                else if (!enabled)
                    tabStatus = "可见但禁用";
                else
                    tabStatus = "可见且启用";
                    
                status += QString("  标签页%1 (级别%2): %3\n").arg(i+1).arg(visibleLevel).arg(tabStatus);
            }
        }
        
        statusText->setText(status);
    }

private:
    CMvTabWidgetExe* tabWidget;
    QSpinBox* levelSpinBox;
    QPushButton* autoDemoButton;
    QTextEdit* statusText;
    QTimer* autoTimer;
    bool autoDemoEnabled;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    DynamicTabTest window;
    window.show();
    
    return app.exec();
}

#include "dynamic_tab_test.moc"
