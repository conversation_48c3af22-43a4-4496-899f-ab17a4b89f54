# Qt TabWidget 标签页隐藏解决方案

由于 `tabBar()->setTabVisible()` 方法在较旧的Qt版本中不存在，本项目提供了多种兼容的解决方案来实现标签页的隐藏功能。

## 问题背景

原始代码尝试使用 `tabBar()->setTabVisible(i, shouldBeVisible)` 来隐藏标签页，但这个方法只在Qt 5.15+版本中可用。为了兼容更多Qt版本，我们提供了以下几种替代方案。

## 解决方案

### 方案1：动态移除/插入标签页方法 (`handleLevelRemoveInsert`) - **推荐用于动态权限**
- **优点**：完全隐藏标签页（包括标签头），支持频繁权限变化，保持标签页状态和选中状态
- **缺点**：性能开销相对较大（但已优化）
- **适用场景**：需要完全隐藏标签页且权限经常变化的情况，如用户权限管理系统

### 方案2：样式表方法 (`handleLevelStyleSheet`)
- **优点**：通过CSS隐藏标签头，保持内容区域隐藏
- **缺点**：依赖样式表，可能与现有样式冲突
- **适用场景**：对视觉效果要求高的情况

### 方案3：自定义TabBar方法 (`handleLevelCustomTabBar`)
- **优点**：完全控制标签页显示，性能好
- **缺点**：需要额外的类，实现复杂
- **适用场景**：需要完全隐藏标签头且性能要求高的情况

### 方案4：简单方法 (`handleLevelSimple`) - **推荐用于一般情况**
- **优点**：简单可靠，兼容所有Qt版本，不会破坏现有功能，性能最佳
- **缺点**：标签头仍然可见（但会变灰显示禁用状态）
- **适用场景**：大多数情况，特别是只需要功能性隐藏且不要求完全隐藏标签头的场景

## 使用方法

### 1. 基本使用

```cpp
CMvTabWidgetExe* tabWidget = new CMvTabWidgetExe();

// 添加标签页
tabWidget->addTab(new QWidget(), "Tab 1");
tabWidget->addTab(new QWidget(), "Tab 2");
tabWidget->addTab(new QWidget(), "Tab 3");

// 设置权限级别信息
// 格式："可见级别-启用级别$-可见级别-启用级别$-..."
tabWidget->setProperty("allTabLevelInfo", "5-3$-8-6$-2-1");

// 方法选择：
// 如果需要完全隐藏标签头且权限经常变化，使用方案1：
tabWidget->handleLevelRemoveInsert(4);

// 如果只需要功能性隐藏，使用方案4（性能更好）：
// tabWidget->handleLevelSimple(4);
```

### 2. 权限级别信息格式

权限信息字符串格式：`"可见级别-启用级别$-可见级别-启用级别$-..."`

例如：`"5-3$-8-6$-2-1"` 表示：
- 第1个标签页：级别5可见，级别3可启用
- 第2个标签页：级别8可见，级别6可启用
- 第3个标签页：级别2可见，级别1可启用

### 3. 动态权限变化

```cpp
// 普通用户（级别1）
tabWidget->handleLevelRemoveInsert(1);

// 高级用户（级别5）
tabWidget->handleLevelRemoveInsert(5);

// 管理员（级别10）
tabWidget->handleLevelRemoveInsert(10);

// 注意：如果标签页结构发生变化（添加/删除标签页），需要重置：
tabWidget->resetOriginalTabs();
```

## 编译和运行

### 使用CMake编译

```bash
mkdir build
cd build
cmake ..
cmake --build .
```

### 运行测试程序

```bash
# 基础功能测试（所有4种方案对比）
./bin/BasicTabTest

# 动态权限变化测试（专注于方案1）
./bin/DynamicTabTest

# 实际使用场景演示
./bin/UsageExample
```

测试程序功能：
- **BasicTabTest**: 对比所有4种隐藏方法的效果
- **DynamicTabTest**: 演示动态权限变化、自动演示、动态添加/删除标签页
- **UsageExample**: 模拟真实的用户权限管理系统

## 文件说明

- `CMvTabWidgetExe_fixed.cpp` - 主要实现文件，包含所有4种方案
- `CMvTabWidgetExe.h` - 头文件声明
- `CustomTabBar.h/cpp` - 自定义TabBar类（方案3使用）
- `test_tab_hiding.cpp` - 基础功能测试程序
- `dynamic_tab_test.cpp` - 动态权限变化测试程序
- `dynamic_usage_example.cpp` - 实际使用场景演示
- `usage_example.cpp` - 基本使用示例代码
- `CMakeLists.txt` - CMake构建文件

## 兼容性

- **Qt版本**：支持Qt 5.6+和Qt 6.x
- **编译器**：支持MSVC、GCC、Clang
- **平台**：Windows、Linux、macOS

## 推荐使用

### 根据需求选择方案：

**如果需要完全隐藏标签头且权限经常变化**，推荐使用**方案1（动态移除/插入）**：

```cpp
tabWidget->handleLevelRemoveInsert(userLevel);
```

**如果只需要功能性隐藏**，推荐使用**方案4（简单方法）**：

```cpp
tabWidget->handleLevelSimple(userLevel);
```

### 方案选择指南：

- **方案1**: 用户权限管理系统、多角色应用、需要完全隐藏标签头
- **方案4**: 一般应用、性能敏感场景、不要求完全隐藏标签头
- **方案3**: 需要完全隐藏但权限变化不频繁的场景
- **方案2**: 对视觉效果有特殊要求的场景

## 注意事项

1. 在调用隐藏方法之前，确保已经设置了 `allTabLevelInfo` 属性
2. 权限级别数字越小表示权限越低
3. 如果标签页数量与权限信息不匹配，会自动进行边界检查
4. 建议在权限变化时重新调用相应的处理方法

### 使用方案1的特别注意事项：

5. **首次调用自动保存**：第一次调用 `handleLevelRemoveInsert` 时会自动保存所有标签页的原始状态
6. **结构变化时重置**：如果动态添加或删除了标签页，需要调用 `resetOriginalTabs()` 重置状态
7. **选中状态保持**：方案1会尽量保持用户当前选中的标签页，如果该标签页被隐藏，会自动选择第一个可见的标签页
8. **性能考虑**：虽然有性能开销，但已经过优化，适合中等频率的权限变化（如用户登录/登出、权限升级等）

### 示例：动态添加标签页后的处理

```cpp
// 添加新标签页
tabWidget->addTab(newWidget, "New Tab");

// 更新权限信息
QString currentInfo = tabWidget->property("allTabLevelInfo").toString();
tabWidget->setProperty("allTabLevelInfo", currentInfo + "$-5-5");

// 重置并重新应用权限
tabWidget->resetOriginalTabs();
tabWidget->handleLevelRemoveInsert(currentUserLevel);
```
