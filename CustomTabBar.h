#ifndef CUSTOMTABBAR_H
#define CUSTOMTABBAR_H

#include <QTabBar>
#include <QPainter>
#include <QStyleOptionTab>
#include <QList>

// 自定义TabBar类，支持隐藏标签页
class CustomTabBar : public QTabBar
{
    Q_OBJECT

public:
    explicit CustomTabBar(QWidget *parent = nullptr);
    
    // 设置隐藏的标签页列表
    void setHiddenTabs(const QList<bool> &hiddenTabs);
    
    // 检查标签页是否隐藏
    bool isTabHidden(int index) const;

protected:
    // 重写绘制事件，跳过隐藏的标签页
    void paintEvent(QPaintEvent *event) override;
    
    // 重写标签页大小计算
    QSize tabSizeHint(int index) const override;
    
    // 重写鼠标事件，忽略隐藏标签页的点击
    void mousePressEvent(QMouseEvent *event) override;

private:
    QList<bool> m_hiddenTabs;  // 存储每个标签页的隐藏状态
};

#endif // CUSTOMTABBAR_H
