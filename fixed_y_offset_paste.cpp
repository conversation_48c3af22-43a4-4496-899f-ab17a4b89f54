// 修正Y偏移问题的完整粘贴代码
else if (keyEvent->key() == Qt::Key_V)
{
    if (m_pPageManager->getCurSelectPage() != m_root_host) return false;

    XMLObject xmlObj;
    QString copyDat = QApplication::clipboard()->text();

    if (!xmlObj.load(copyDat, nullptr))
    {
        return false;
    }

    bool hasValidHosts = false;
    QUndoCommand* cmd = new QUndoCommand;

    // 获取目标父类 - 统一使用m_root_host作为基准
    CMvAbstractHost* targetParent = m_root_host;

    // 获取鼠标位置 ==========
    // 获取鼠标当前位置（全局坐标）
    QPoint globalMousePos = QCursor::pos();

    // 将全局坐标转换为m_root_host的本地坐标
    QWidget* rootWidget = (QWidget *)m_root_host->getObject();
    QPoint localMousePos;

    if (rootWidget)
    {
        localMousePos = rootWidget->mapFromGlobal(globalMousePos);

        // 调试输出 - 可以在发布版本中删除
        qDebug() << "Global mouse pos:" << globalMousePos;
        qDebug() << "Local mouse pos (relative to root):" << localMousePos;
        qDebug() << "Root widget rect:" << rootWidget->rect();
    }
    else
    {
        // 如果无法获取根widget，使用全局坐标
        localMousePos = globalMousePos;
        qDebug() << "Warning: Could not get root widget, using global coordinates";
    }

    // 获取复制的控件列表
    QList<int> index;
    QList<CMvAbstractHost*> list;
    QList<XMLObject*> xmlHosts = xmlObj.getChildren();

    // 单次遍历解决方案：同时计算topLeftMost和创建控件 ==========
    QPoint topLeftMost;
    bool firstPoint = true;
    QList<QRect> originalGeometries; // 存储原始几何信息

    // Y方向偏移修正 - 重置为0进行调试
    int yCorrection = 0;
    if (targetWidget)
    {
        // 暂时不应用修正，先观察基础位置
        yCorrection = 0;

        qDebug() << "Y correction applied:" << yCorrection;
    }

    // 单次遍历：创建控件并同时计算topLeftMost
    for (int i = 0; i < xmlHosts.size(); ++i)
    {
        XMLObject* xmlHost = xmlHosts[i];
        CMvAbstractHost* pHostObj = CMvHostFactory::createHost(xmlHost);
        if (pHostObj != nullptr)
        {
            pHostObj->generateLayoutInAdvance(xmlHost);
            pHostObj->fromObject(xmlHost);
            pHostObj->setDefault();
            pHostObj->setId(targetParent, m_pPageManager);

            // 获取原始几何信息
            QRect originalGeometry = pHostObj->getPropertyValue("geometry").toRect();
            originalGeometries.append(originalGeometry);

            // 同时计算topLeftMost
            QPoint topLeft = originalGeometry.topLeft();
            if (firstPoint)
            {
                topLeftMost = topLeft;
                firstPoint = false;
            }
            else
            {
                // 找到最左上角的点
                if (topLeft.x() < topLeftMost.x())
                    topLeftMost.setX(topLeft.x());
                if (topLeft.y() < topLeftMost.y())
                    topLeftMost.setY(topLeft.y());
            }

            list.append(pHostObj);
            index.append(targetParent->getChildCount() + i);
            hasValidHosts = true;
        }
    }

    // 计算偏移量：鼠标位置 - 控件组的最左上角
    QPoint mouseOffset = localMousePos - topLeftMost;

    // 应用Y偏移修正来补偿10像素对齐的影响
    // 由于控件会自动对齐到10像素网格，我们需要预先调整
    mouseOffset.setY(mouseOffset.y() + yCorrection);

    qDebug() << "Mouse offset before alignment compensation:" << mouseOffset;

    // 如果不是第一次粘贴，添加额外偏移避免重叠
    if (!m_isFirstPasteAfterCopy)
    {
        mouseOffset += QPoint(20, 20);
    }

    // 详细调试输出
    qDebug() << "=== 位置计算调试 ===";
    qDebug() << "Global mouse pos:" << QCursor::pos();
    qDebug() << "Local mouse pos:" << localMousePos;
    qDebug() << "Top left most:" << topLeftMost;
    qDebug() << "Mouse offset (after correction):" << mouseOffset;
    qDebug() << "Expected final position:" << (topLeftMost + mouseOffset);

    // 应用计算出的偏移到所有控件
    for (int i = 0; i < list.size() && i < originalGeometries.size(); ++i)
    {
        CMvAbstractHost* pHostObj = list[i];
        QRect originalGeometry = originalGeometries[i];

        // 计算新位置：原始位置 + 鼠标偏移
        QRect newGeometry = originalGeometry;
        newGeometry.translate(mouseOffset);

        qDebug() << "Control" << i << "- Before boundary check:" << newGeometry;

        // 边界检查，确保不超出根控件范围
        QRect beforeBoundaryCheck = newGeometry;
        if (rootWidget)
        {
            QRect rootRect = rootWidget->rect();

            // 调整位置确保在根控件内
            if (newGeometry.right() > rootRect.right())
                newGeometry.moveRight(rootRect.right());
            if (newGeometry.bottom() > rootRect.bottom())
                newGeometry.moveBottom(rootRect.bottom());
            if (newGeometry.left() < rootRect.left())
                newGeometry.moveLeft(rootRect.left());
            if (newGeometry.top() < rootRect.top())
                newGeometry.moveTop(rootRect.top());
        }

        if (beforeBoundaryCheck != newGeometry) {
            qDebug() << "Control" << i << "- Boundary check changed position from" << beforeBoundaryCheck << "to" << newGeometry;
        }

        // 设置新的几何位置
        pHostObj->setPropertyValue("geometry", newGeometry);

        // 检查设置后的实际位置
        QRect actualGeometry = pHostObj->getPropertyValue("geometry").toRect();

        // 调试输出
        qDebug() << "Control" << i << "- Original:" << originalGeometry;
        qDebug() << "Control" << i << "- Calculated:" << newGeometry;
        qDebug() << "Control" << i << "- Actual after set:" << actualGeometry;

        if (newGeometry != actualGeometry) {
            qDebug() << "*** POSITION CHANGED AFTER setPropertyValue! ***";
            qDebug() << "*** Difference:" << (actualGeometry.topLeft() - newGeometry.topLeft()) << "***";
        }

        // 更新最后粘贴位置记录
        if (i < m_lastPastePositions.size())
        {
            m_lastPastePositions[i] = newGeometry.center();
        }
        else
        {
            m_lastPastePositions.append(newGeometry.center());
        }
    }

    // 第一次粘贴完成后标记
    if (hasValidHosts && m_isFirstPasteAfterCopy)
    {
        m_isFirstPasteAfterCopy = false;
    }

    if (hasValidHosts)
    {
        new CMvAddHostUndoCommand(m_pPageManager, targetParent, list, index, AHT_ADD, cmd);
        m_pUndoStack->push(cmd);

        QList<CMvAbstractHost*> allHost;
        for (auto host : list)
        {
            host->getAllDescendants(allHost);
        }

        for (auto host : allHost)
        {
            QString dataBind = host->getPropertyValue("dataBind").toString();
            if (!dataBind.isEmpty())
            {
                QVariant var(dataBind);
                host->generateEventCode("dataBind", var, m_pPageManager, false);
            }

            QString suffix = "";
            QStringList eventList;
            host->getEventPropertyName(suffix, eventList);
            for (auto str : eventList)
            {
                QString element = host->getPropertyValue(str).toString();
                if (!element.isEmpty())
                {
                    QVariant var(element);
                    host->generateEventCode(str, var, m_pPageManager, false);
                }
            }
        }
    }
}

/*
调试和调整步骤：

1. 运行代码，观察调试输出
2. 测试粘贴功能，观察Y方向的偏移量
3. 调整 yCorrection 的值：
   - 如果控件粘贴位置偏下，增加负值（如 -40）
   - 如果控件粘贴位置偏上，减少负值或使用正值（如 -10 或 10）
4. 重复测试直到位置准确

常见的偏移值：
- 窗口标题栏：通常 20-30 像素
- 菜单栏：通常 20-25 像素  
- 工具栏：通常 30-40 像素
- 状态栏：通常 20-25 像素

建议从 yCorrection = -30 开始测试，然后根据实际效果调整。
*/
