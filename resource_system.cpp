#include "resource_system.h"
#include <algorithm>
#include <sstream>

namespace ResourceSystem {

void ResourceManager::registerResource(const std::string& path, const unsigned char* data, size_t size) {
    std::string normalizedPath = normalizePath(path);
    resources_[normalizedPath] = std::make_shared<ResourceData>(data, size, normalizedPath);
    std::cout << "Registered resource: " << normalizedPath << " (" << size << " bytes)" << std::endl;
}

std::shared_ptr<ResourceData> ResourceManager::getResource(const std::string& path) const {
    std::string normalizedPath = normalizePath(path);
    auto it = resources_.find(normalizedPath);
    return (it != resources_.end()) ? it->second : nullptr;
}

bool ResourceManager::hasResource(const std::string& path) const {
    std::string normalizedPath = normalizePath(path);
    return resources_.find(normalizedPath) != resources_.end();
}

std::string ResourceManager::getResourceAsString(const std::string& path) const {
    auto resource = getResource(path);
    if (!resource) {
        return "";
    }
    return std::string(reinterpret_cast<const char*>(resource->data), resource->size);
}

std::vector<unsigned char> ResourceManager::getResourceAsBytes(const std::string& path) const {
    auto resource = getResource(path);
    if (!resource) {
        return {};
    }
    return std::vector<unsigned char>(resource->data, resource->data + resource->size);
}

std::vector<std::string> ResourceManager::listResources() const {
    std::vector<std::string> paths;
    paths.reserve(resources_.size());
    for (const auto& pair : resources_) {
        paths.push_back(pair.first);
    }
    std::sort(paths.begin(), paths.end());
    return paths;
}

std::vector<std::string> ResourceManager::listResourcesInDirectory(const std::string& directory) const {
    std::string normalizedDir = normalizePath(directory);
    if (!normalizedDir.empty() && normalizedDir.back() != '/') {
        normalizedDir += '/';
    }
    
    std::vector<std::string> paths;
    for (const auto& pair : resources_) {
        if (pair.first.find(normalizedDir) == 0) {
            paths.push_back(pair.first);
        }
    }
    std::sort(paths.begin(), paths.end());
    return paths;
}

bool ResourceManager::saveResourceToFile(const std::string& resourcePath, const std::string& filePath) const {
    auto resource = getResource(resourcePath);
    if (!resource) {
        return false;
    }
    
    std::ofstream file(filePath, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }
    
    file.write(reinterpret_cast<const char*>(resource->data), resource->size);
    return file.good();
}

std::string ResourceManager::normalizePath(const std::string& path) const {
    std::string normalized = path;
    
    // 确保路径以/开头
    if (normalized.empty() || normalized[0] != '/') {
        normalized = "/" + normalized;
    }
    
    // 替换反斜杠为正斜杠
    std::replace(normalized.begin(), normalized.end(), '\\', '/');
    
    // 移除重复的斜杠
    std::string result;
    bool lastWasSlash = false;
    for (char c : normalized) {
        if (c == '/') {
            if (!lastWasSlash) {
                result += c;
                lastWasSlash = true;
            }
        } else {
            result += c;
            lastWasSlash = false;
        }
    }
    
    return result;
}

// 便利函数实现
std::string loadResourceAsString(const std::string& path) {
    return ResourceManager::instance().getResourceAsString(path);
}

std::vector<unsigned char> loadResourceAsBytes(const std::string& path) {
    return ResourceManager::instance().getResourceAsBytes(path);
}

bool saveResourceToFile(const std::string& resourcePath, const std::string& filePath) {
    return ResourceManager::instance().saveResourceToFile(resourcePath, filePath);
}

} // namespace ResourceSystem

// QResource类实现
QResource::QResource(const std::string& path) : path_(path) {
    resource_ = ResourceSystem::ResourceManager::instance().getResource(path);
}

bool QResource::isValid() const {
    return resource_ != nullptr;
}

const unsigned char* QResource::data() const {
    return resource_ ? resource_->data : nullptr;
}

size_t QResource::size() const {
    return resource_ ? resource_->size : 0;
}

std::string QResource::path() const {
    return path_;
}
