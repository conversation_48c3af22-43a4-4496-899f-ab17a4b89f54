#include <iostream>
#include <string>
#include <vector>
#include "resource_system.h"

// 手动嵌入一些测试数据
static const unsigned char test_data_1[] = {
    0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x20, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x21, 0x0a
};
static const size_t test_data_1_size = 13;

static const unsigned char test_data_2[] = {
    0x7b, 0x22, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x3a, 0x20, 0x22, 0x74, 0x65, 0x73, 0x74, 0x22, 0x7d
};
static const size_t test_data_2_size = 16;

// 注册测试资源
namespace {
    struct TestResourceRegistrar {
        TestResourceRegistrar() {
            auto& rm = ResourceSystem::ResourceManager::instance();
            rm.registerResource("/test/hello.txt", test_data_1, test_data_1_size);
            rm.registerResource("/config/test.json", test_data_2, test_data_2_size);
        }
    };
    static TestResourceRegistrar g_testRegistrar;
}

int main() {
    std::cout << "=== Resource System Test ===" << std::endl;
    
    auto& rm = ResourceSystem::ResourceManager::instance();
    
    // 测试资源列举
    std::cout << "\n1. 列出所有资源:" << std::endl;
    auto resources = rm.listResources();
    for (const auto& path : resources) {
        std::cout << "  " << path << std::endl;
    }
    
    // 测试资源访问
    std::cout << "\n2. 测试资源访问:" << std::endl;
    
    // 测试文本资源
    if (rm.hasResource("/test/hello.txt")) {
        std::string content = rm.getResourceAsString("/test/hello.txt");
        std::cout << "  /test/hello.txt: \"" << content << "\"" << std::endl;
    }
    
    // 测试JSON资源
    if (rm.hasResource("/config/test.json")) {
        std::string json = rm.getResourceAsString("/config/test.json");
        std::cout << "  /config/test.json: " << json << std::endl;
    }
    
    // 测试QResource接口
    std::cout << "\n3. 测试QResource接口:" << std::endl;
    QResource resource("/test/hello.txt");
    if (resource.isValid()) {
        std::cout << "  路径: " << resource.path() << std::endl;
        std::cout << "  大小: " << resource.size() << " bytes" << std::endl;
        std::cout << "  内容: \"" << std::string(reinterpret_cast<const char*>(resource.data()), resource.size()) << "\"" << std::endl;
    }
    
    // 测试便利函数
    std::cout << "\n4. 测试便利函数:" << std::endl;
    std::string content = ResourceSystem::loadResourceAsString("/config/test.json");
    if (!content.empty()) {
        std::cout << "  loadResourceAsString: " << content << std::endl;
    }
    
    auto bytes = ResourceSystem::loadResourceAsBytes("/test/hello.txt");
    if (!bytes.empty()) {
        std::cout << "  loadResourceAsBytes: " << bytes.size() << " bytes" << std::endl;
    }
    
    // 测试目录列举
    std::cout << "\n5. 测试目录列举:" << std::endl;
    auto testResources = rm.listResourcesInDirectory("/test");
    std::cout << "  /test 目录下的资源:" << std::endl;
    for (const auto& path : testResources) {
        std::cout << "    " << path << std::endl;
    }
    
    // 测试保存到文件
    std::cout << "\n6. 测试保存到文件:" << std::endl;
    if (rm.saveResourceToFile("/test/hello.txt", "extracted_hello.txt")) {
        std::cout << "  成功保存 /test/hello.txt 到 extracted_hello.txt" << std::endl;
    } else {
        std::cout << "  保存失败" << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}
