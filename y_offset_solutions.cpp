// Y方向偏移问题的几种解决方案

// 方案1：手动调整Y偏移
// 在计算偏移量后添加Y方向的修正
QPoint mouseOffset = localMousePos - topLeftMost;

// 手动调整Y偏移 - 根据实际测试调整这个值
int yCorrection = -30; // 负值向上调整，正值向下调整，根据实际情况调整
mouseOffset.setY(mouseOffset.y() + yCorrection);

// 如果不是第一次粘贴，添加额外偏移避免重叠
if (!m_isFirstPasteAfterCopy)
{
    mouseOffset += QPoint(20, 20);
}

//=============================================================================

// 方案2：使用不同的坐标转换方法
// 替换原来的坐标转换部分
QPoint localMousePos;
if (targetWidget)
{
    // 方法A：直接使用mapFromGlobal
    localMousePos = targetWidget->mapFromGlobal(globalMousePos);
    
    // 方法B：考虑窗口装饰的影响
    // QWidget* topLevelWidget = targetWidget->window();
    // QPoint windowLocalPos = topLevelWidget->mapFromGlobal(globalMousePos);
    // localMousePos = targetWidget->mapFrom(topLevelWidget, windowLocalPos);
    
    // 方法C：使用相对于父控件的坐标
    // if (targetWidget->parentWidget())
    // {
    //     QPoint parentPos = targetWidget->parentWidget()->mapFromGlobal(globalMousePos);
    //     localMousePos = targetWidget->mapFromParent(parentPos);
    // }
    // else
    // {
    //     localMousePos = targetWidget->mapFromGlobal(globalMousePos);
    // }
}

//=============================================================================

// 方案3：动态计算Y偏移修正值
// 在找到topLeftMost后，动态计算修正值
QPoint mouseOffset = localMousePos - topLeftMost;

// 动态计算Y修正值
if (targetWidget)
{
    // 获取控件的实际可用区域（排除标题栏等）
    QRect clientRect = targetWidget->contentsRect();
    QRect widgetRect = targetWidget->rect();
    
    // 计算标题栏等装饰的高度
    int decorationHeight = widgetRect.height() - clientRect.height();
    
    // 应用修正
    mouseOffset.setY(mouseOffset.y() - decorationHeight);
    
    qDebug() << "Widget rect:" << widgetRect;
    qDebug() << "Contents rect:" << clientRect;
    qDebug() << "Decoration height:" << decorationHeight;
}

//=============================================================================

// 方案4：使用事件位置而不是鼠标位置
// 如果您可以在鼠标事件处理中获取事件位置，这样更准确
// 假设您有鼠标事件的位置信息
// QPoint eventPos = mouseEvent->pos(); // 来自鼠标事件
// QPoint localMousePos = targetWidget->mapFromGlobal(targetWidget->mapToGlobal(eventPos));

//=============================================================================

// 方案5：考虑滚动区域的影响
// 如果目标控件在滚动区域内
QPoint mouseOffset = localMousePos - topLeftMost;

if (targetWidget)
{
    // 检查是否有滚动条影响
    QScrollArea* scrollArea = qobject_cast<QScrollArea*>(targetWidget->parentWidget());
    if (scrollArea)
    {
        // 获取滚动偏移
        QPoint scrollOffset = QPoint(scrollArea->horizontalScrollBar()->value(),
                                   scrollArea->verticalScrollBar()->value());
        
        // 应用滚动偏移修正
        mouseOffset += scrollOffset;
        
        qDebug() << "Scroll offset:" << scrollOffset;
    }
}

//=============================================================================

// 方案6：最简单的测试方案 - 直接减去固定值
// 用于快速测试和确定偏移量
QPoint mouseOffset = localMousePos - topLeftMost;

// 简单的Y偏移修正 - 根据您观察到的偏移量调整
mouseOffset.setY(mouseOffset.y() - 25); // 假设偏移了25像素，根据实际情况调整

// 如果不是第一次粘贴，添加额外偏移避免重叠
if (!m_isFirstPasteAfterCopy)
{
    mouseOffset += QPoint(20, 20);
}

//=============================================================================

// 推荐的调试代码 - 添加到您的代码中帮助定位问题
qDebug() << "=== 粘贴位置调试信息 ===";
qDebug() << "Global mouse pos:" << globalMousePos;
qDebug() << "Local mouse pos:" << localMousePos;
qDebug() << "Top left most:" << topLeftMost;
qDebug() << "Mouse offset:" << mouseOffset;
qDebug() << "Target widget rect:" << (targetWidget ? targetWidget->rect() : QRect());
qDebug() << "Target widget geometry:" << (targetWidget ? targetWidget->geometry() : QRect());

// 在设置新几何位置后也添加调试
qDebug() << "Original geometry:" << originalGeometry;
qDebug() << "New geometry:" << newGeometry;
qDebug() << "========================";

/*
使用建议：

1. 首先尝试方案6（最简单的固定偏移修正）
2. 根据调试输出确定具体的偏移量
3. 如果固定偏移不够准确，尝试方案3（动态计算）
4. 如果问题仍然存在，可能需要检查：
   - 窗口的标题栏高度
   - 菜单栏高度
   - 工具栏高度
   - 滚动区域的影响
   - 不同DPI设置的影响

调试步骤：
1. 添加调试输出代码
2. 运行程序并观察输出
3. 记录实际偏移量
4. 应用相应的修正值
*/
