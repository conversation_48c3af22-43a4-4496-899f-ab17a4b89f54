#include "CustomTabBar.h"
#include <QMouseEvent>
#include <QStylePainter>

CustomTabBar::CustomTabBar(QWidget *parent)
    : QTabBar(parent)
{
}

void CustomTabBar::setHiddenTabs(const QList<bool> &hiddenTabs)
{
    m_hiddenTabs = hiddenTabs;
    update(); // 触发重绘
}

bool CustomTabBar::isTabHidden(int index) const
{
    if (index >= 0 && index < m_hiddenTabs.size())
    {
        return m_hiddenTabs[index];
    }
    return false;
}

void CustomTabBar::paintEvent(QPaintEvent *event)
{
    QStylePainter painter(this);
    
    for (int i = 0; i < count(); ++i)
    {
        // 跳过隐藏的标签页
        if (isTabHidden(i))
        {
            continue;
        }
        
        QStyleOptionTab option;
        initStyleOption(&option, i);
        painter.drawControl(QStyle::CE_TabBarTab, option);
    }
}

QSize CustomTabBar::tabSizeHint(int index) const
{
    // 隐藏的标签页返回0大小
    if (isTabHidden(index))
    {
        return QSize(0, 0);
    }
    
    return QTabBar::tabSizeHint(index);
}

void CustomTabBar::mousePressEvent(QMouseEvent *event)
{
    // 检查点击的标签页是否隐藏
    int clickedTab = tabAt(event->pos());
    if (clickedTab >= 0 && isTabHidden(clickedTab))
    {
        // 忽略对隐藏标签页的点击
        return;
    }
    
    QTabBar::mousePressEvent(event);
}
