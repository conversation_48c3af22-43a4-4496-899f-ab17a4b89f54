#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <fstream>
#include <iostream>

/**
 * 模拟Qt QRC资源系统的实现
 * 
 * 主要功能：
 * 1. 将文件内容嵌入到程序中
 * 2. 提供统一的资源访问接口
 * 3. 支持路径别名和目录结构
 */

namespace ResourceSystem {

// 资源数据结构
struct ResourceData {
    const unsigned char* data;
    size_t size;
    std::string path;
    
    ResourceData(const unsigned char* d, size_t s, const std::string& p)
        : data(d), size(s), path(p) {}
};

// 资源管理器类
class ResourceManager {
public:
    static ResourceManager& instance() {
        static ResourceManager instance;
        return instance;
    }
    
    // 注册资源
    void registerResource(const std::string& path, const unsigned char* data, size_t size);
    
    // 获取资源数据
    std::shared_ptr<ResourceData> getResource(const std::string& path) const;
    
    // 检查资源是否存在
    bool hasResource(const std::string& path) const;
    
    // 获取资源内容为字符串
    std::string getResourceAsString(const std::string& path) const;
    
    // 获取资源内容为字节数组
    std::vector<unsigned char> getResourceAsBytes(const std::string& path) const;
    
    // 列出所有资源路径
    std::vector<std::string> listResources() const;
    
    // 列出指定目录下的资源
    std::vector<std::string> listResourcesInDirectory(const std::string& directory) const;
    
    // 保存资源到文件
    bool saveResourceToFile(const std::string& resourcePath, const std::string& filePath) const;

private:
    ResourceManager() = default;
    std::unordered_map<std::string, std::shared_ptr<ResourceData>> resources_;
    
    // 标准化路径（确保以/开头）
    std::string normalizePath(const std::string& path) const;
};

// 资源注册宏，用于自动注册资源
#define REGISTER_RESOURCE(path, data, size) \
    namespace { \
        struct ResourceRegistrar_##__LINE__ { \
            ResourceRegistrar_##__LINE__() { \
                ResourceSystem::ResourceManager::instance().registerResource(path, data, size); \
            } \
        }; \
        static ResourceRegistrar_##__LINE__ registrar_##__LINE__; \
    }

// 便利函数，模拟Qt的qrc://语法
std::string loadResourceAsString(const std::string& path);
std::vector<unsigned char> loadResourceAsBytes(const std::string& path);
bool saveResourceToFile(const std::string& resourcePath, const std::string& filePath);

} // namespace ResourceSystem

// 全局便利函数，模拟Qt的QResource类
class QResource {
public:
    explicit QResource(const std::string& path);
    
    bool isValid() const;
    const unsigned char* data() const;
    size_t size() const;
    std::string path() const;
    
private:
    std::shared_ptr<ResourceSystem::ResourceData> resource_;
    std::string path_;
};
