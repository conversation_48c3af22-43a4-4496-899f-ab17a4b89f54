#!/usr/bin/env python3
"""
资源生成器 - 模拟Qt rcc工具
将文件转换为C++代码，嵌入到程序中

用法:
python resource_generator.py config.json output.cpp
或
python resource_generator.py --qrc resources.qrc output.cpp
"""

import json
import os
import sys
import argparse
import xml.etree.ElementTree as ET
from pathlib import Path

def file_to_cpp_array(file_path, var_name):
    """将文件转换为C++字节数组"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # 生成C++数组
        cpp_array = f"static const unsigned char {var_name}[] = {{\n"
        
        # 每行16个字节
        for i in range(0, len(data), 16):
            line = "    "
            for j in range(16):
                if i + j < len(data):
                    line += f"0x{data[i + j]:02x}, "
                else:
                    break
            cpp_array += line + "\n"
        
        cpp_array += "};\n"
        cpp_array += f"static const size_t {var_name}_size = {len(data)};\n\n"
        
        return cpp_array, len(data)
    
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return "", 0

def sanitize_var_name(path):
    """将路径转换为有效的C++变量名"""
    # 移除路径分隔符和特殊字符
    name = path.replace('/', '_').replace('\\', '_').replace('.', '_').replace('-', '_')
    # 确保以字母开头
    if name and not name[0].isalpha():
        name = 'res_' + name
    return name

def parse_qrc_file(qrc_path):
    """解析Qt .qrc文件"""
    resources = []
    base_dir = os.path.dirname(qrc_path)
    
    try:
        tree = ET.parse(qrc_path)
        root = tree.getroot()
        
        for qresource in root.findall('qresource'):
            prefix = qresource.get('prefix', '')
            
            for file_elem in qresource.findall('file'):
                file_path = file_elem.text.strip()
                alias = file_elem.get('alias', file_path)
                
                # 构建完整路径
                full_path = os.path.join(base_dir, file_path)
                resource_path = f"/{prefix}/{alias}".replace('//', '/')
                
                if os.path.exists(full_path):
                    resources.append({
                        'file_path': full_path,
                        'resource_path': resource_path
                    })
                else:
                    print(f"Warning: File not found: {full_path}")
    
    except Exception as e:
        print(f"Error parsing QRC file: {e}")
        return []
    
    return resources

def parse_json_config(json_path):
    """解析JSON配置文件"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        resources = []
        base_dir = os.path.dirname(json_path)
        
        for item in config.get('resources', []):
            file_path = item['file']
            resource_path = item['path']
            
            # 如果是相对路径，基于配置文件目录
            if not os.path.isabs(file_path):
                file_path = os.path.join(base_dir, file_path)
            
            if os.path.exists(file_path):
                resources.append({
                    'file_path': file_path,
                    'resource_path': resource_path
                })
            else:
                print(f"Warning: File not found: {file_path}")
        
        return resources
    
    except Exception as e:
        print(f"Error parsing JSON config: {e}")
        return []

def generate_cpp_file(resources, output_path):
    """生成C++资源文件"""
    cpp_content = '''// Auto-generated resource file
// Do not edit manually

#include "resource_system.h"

namespace {

'''
    
    registrations = []
    
    for i, resource in enumerate(resources):
        var_name = f"resource_data_{i}"
        file_path = resource['file_path']
        resource_path = resource['resource_path']
        
        print(f"Processing: {file_path} -> {resource_path}")
        
        array_code, size = file_to_cpp_array(file_path, var_name)
        if array_code:
            cpp_content += array_code
            registrations.append(f'    ResourceSystem::ResourceManager::instance().registerResource("{resource_path}", {var_name}, {var_name}_size);')
    
    # 添加注册函数
    cpp_content += '''
// Resource registration function
void registerResources() {
'''
    cpp_content += '\n'.join(registrations)
    cpp_content += '''
}

// Auto-registration using static initialization
struct ResourceRegistrar {
    ResourceRegistrar() {
        registerResources();
    }
};

static ResourceRegistrar g_resourceRegistrar;

} // anonymous namespace
'''
    
    # 写入文件
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(cpp_content)
        print(f"Generated: {output_path}")
        return True
    except Exception as e:
        print(f"Error writing output file: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Resource Generator - Convert files to embedded C++ resources')
    parser.add_argument('input', help='Input file (JSON config or QRC file)')
    parser.add_argument('output', help='Output C++ file')
    parser.add_argument('--qrc', action='store_true', help='Input is a QRC file')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"Error: Input file not found: {args.input}")
        return 1
    
    # 解析输入文件
    if args.qrc or args.input.endswith('.qrc'):
        resources = parse_qrc_file(args.input)
    else:
        resources = parse_json_config(args.input)
    
    if not resources:
        print("No resources found or error parsing input file")
        return 1
    
    # 生成C++文件
    if generate_cpp_file(resources, args.output):
        print(f"Successfully generated {len(resources)} resources")
        return 0
    else:
        return 1

if __name__ == '__main__':
    sys.exit(main())
